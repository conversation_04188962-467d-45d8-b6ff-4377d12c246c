#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数仓数据导出工具
从Hologres数据库提取数据并导出为CSV文件
"""

import psycopg2
import pandas as pd
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataWarehouseExporter:
    def __init__(self, environment='UAT'):
        """
        初始化数据仓库导出器
        
        Args:
            environment (str): 环境标识，'UAT' 或 'PROD'
        """
        self.environment = environment
        self.conn = None
        self.timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        # 数据库连接配置
        self.db_config = {
            'host': "hgpostcn-cn-wwo3iu4fu008-cn-shanghai.hologres.aliyuncs.com",
            'port': 80,
            'dbname': "hologres_prd",
            'user': "LTAI5tJiSFcRXENKTP7NdVbg",
            'password': "******************************",
            'keepalives': 1,
            'keepalives_idle': 130,
            'keepalives_interval': 10,
            'keepalives_count': 15,
        }
        
        # 表配置
        self.table_configs = {
            'store_sku_full': {
                'table_name': 'rpt_supply_hla_elem_store_sku_full_d',
                'columns': [
                    'ds', 'sku_code', 'sku_name', 'brand_store_code', 'brand_store_name',
                    'elem_store_code', 'elem_store_name', 'is_exist', 'is_exist_out_of_stock',
                    'is_exist_out_of_stock_days', 'is_exist_out_of_stock_longtemp',
                    'is_unsold_days', 'is_unsold_longtemp'
                ],
                'csv_headers': [
                    '日期', 'SKU code', 'SKU name', '门店H码', '门店名称',
                    '饿了么门店编码', '饿了么门店名称', '商品是否上架', '商品是否上架但缺货',
                    '商品连续上架但缺货天数', '商品是否长期缺货',
                    '商品连续未动销天数', '商品是否长期未动销'
                ],
                'filename_prefix': 'O2ODATA'
            },
            'mt_sku_vender_agg': {
                'table_name': 'rpt_supply_hla_mt_sku_vender_agg_d',
                'columns': [
                    'statistical_period', 'comparison_period', 'chain_tag', 'chain_brand',
                    'vender_name', 'vender_type', 'brand_name', 'product_name', 'category',
                    'brand', 'sku_name', 'sku_code', 'product_group', 'product_group_code',
                    'product_group_description', 'province', 'city', 'sales_region',
                    'transaction_heat_index', 'transaction_heat_change', 'sales_heat_index',
                    'sales_heat_change', 'distribution_store_count', 'o2o_store_num',
                    'store_distribution_rate', 'available_store_count', 'out_of_stock_rate',
                    'active_store_count', 'onsale_store_sales_rate', 'platform_store_sales_rate'
                ],
                'csv_headers': [
                    '统计周期', '对比周期', '连锁标签', '连锁品牌', '连锁总部名称', 'NKA/LKA/NRT',
                    '品牌名称', '商品名称', 'Category', 'Brand', 'SKU', 'SKU CODE',
                    'SKU产品组', 'ProductGroupCode', 'ProductGroupDescription', '省份',
                    '城市', '大区', '交易热力值', '交易热力值较对比期变化', '商品销量热力值',
                    '商品销量热力值较对比期变化', '铺货门店数', '平台门店数', '门店铺货率',
                    '上架可售门店数', '门店缺货率', '动销门店数', '铺货门店上架可售率%', '平台门店上架可售率%'
                ],
                'filename_prefix': 'O2O_Eleme_Daily'
            },
            'elem_store_sku_agg': {
                'table_name': 'rpt_supply_hla_elem_sku_region_agg_d',
                'columns': [
                    'date', 'category', 'brand', 'sku_name', 'sku_code',
                    'sku_product_group', 'sku_product_group_code', 'sku_product_group_description',
                    'sales_region', 'province', 'city', 'hla_shop_brand_name',
                    'hla_sub_shop_brand_id', 'hla_sub_shop_brand_name', 'o2o_shop_num',
                    'iscreate_shop_num', 'isonsale_shop_num', 'isonsale_shop_num',
                    'active_shop_num', 'out_of_stock_shop_num',
                    'onsale_store_sales_rate', 'platform_store_sales_rate'
                ],
                'csv_headers': [
                    '日期', 'category', 'brand', 'sku', 'sku_code',
                    'sku产品组', 'productgroupcode', 'productgroupdescription',
                    '大区', '省份', '城市', '连锁总部名称',
                    '二级分部编码', '二级分部名称', 'o2o门店数', '建品门店数',
                    '上架门店数', '可售门店数', '动销门店数', '缺货门店数',
                    '铺货门店上架可售率', '平台门店上架可售率'
                ],
                'filename_prefix': 'O2O_Eleme_Daily'
            },
            'elem_sku_region_agg': {
                'table_name': 'rpt_supply_hla_elem_store_sku_detail_d',
                'columns': [
                    'ds', 'id', 'item_id', 'item_title', 'upc', 'upc_name', 'category',
                    'brand', 'sku_name', 'sku_code', 'product_group', 'product_group_code',
                    'product_group_description', 'ele_cate_level1_id', 'ele_cate_level1_name',
                    'NULL as ele_cate_level2_id', 'NULL as ele_cate_level2_name',
                    'NULL as ele_cate_level3_id', 'NULL as ele_cate_level3_name',
                    'NULL as brand_id', 'NULL as brand_name_detail',
                    'NULL as second_work_entity_id', 'NULL as second_work_entity_name',
                    'NULL as shop_industry', 'NULL as shop_type', 'NULL as elem_shop_code',
                    'NULL as brand_shop_code', 'NULL as elem_shop_name', 'NULL as brand_shop_name',
                    'province_name', 'city_name', 'NULL as city_detail',
                    'NULL as chain_headquarters_name', 'NULL as second_branch_id', 'NULL as second_branch_name',
                    'NULL as is_o2o_strategy_shop', 'NULL as shop_address', 'NULL as longitude',
                    'NULL as latitude', 'NULL as province_id', 'NULL as province_name',
                    'NULL as city_id', 'NULL as city_name', 'NULL as district_id', 'NULL as district_name',
                    'NULL as merchant_brand_id', 'NULL as merchant_brand_name', 'NULL as is_product_created',
                    'NULL as create_time', 'NULL as is_online', 'NULL as is_sellable',
                    'NULL as has_stock', 'NULL as is_out_of_stock', 'NULL as has_main_image',
                    'NULL as has_other_images', 'NULL as has_sales', 'NULL as is_front_warehouse',
                    'NULL as online_status', 'NULL as shop_status', 'NULL as stock_level',
                    'is_exist_out_of_stock_days', 'NULL as long_term_out_of_stock_shop',
                    'is_unexist_days', 'NULL as long_term_offline_shop',
                    'is_unsold_days', 'NULL as long_term_unsold_shop'
                ],
                'csv_headers': [
                    '日期', 'id', '渠道商品id', '渠道商品名称', 'upc', '商品名称', 'Category',
                    'Brand', 'SKU', 'SKU code', 'SKU产品组', 'ProductGroupCode',
                    'ProductGroupDescription', '一级类目id', '一级类目名称', '二级类目id', '二级类目名称',
                    '三级类目id', '三级类目名称', '品牌ID', '品牌名称', '二级作业实体ID', '二级作业实体名称',
                    '店铺所属行业', '门店类型', '门店编码（饿了么）', '门店编码（品牌）', '门店名称（饿了么）', '门店名称（品牌）',
                    '大区', '省份', '城市', '连锁总部名称', '二级分布ID', '二级分部名称',
                    '是否O2O策略门店', '门店地址', '经度', '纬度', '省份ID', '省份名称',
                    '城市ID', '城市名称', '区县ID', '区县名称', '商户品牌ID', '商户品牌名称',
                    '商品是否建品', '创建时间', '商品是否上架', '商品是否可售', '商品是否有库存',
                    '是否缺货', '商品是否有主图', '商品是否有其他图', '商品是否有动销', '是否前置仓/店',
                    '在线状态', '门店状态', '库存等级', '连续上架但缺货天数', '长期上架但缺货门店',
                    '连续未上架天数', '长期未上架门店', '连续未动销天数', '长期未动销门店'
                ],
                'filename_prefix': 'O2O_ElemeDetail_Daily'
            }
        }

    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = psycopg2.connect(**self.db_config)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def disconnect_database(self):
        """断开数据库连接"""
        if self.conn:
            self.conn.close()
            logger.info("数据库连接已关闭")

    def execute_query(self, query):
        """执行SQL查询"""
        try:
            df = pd.read_sql_query(query, self.conn)
            logger.info(f"查询执行成功，返回 {len(df)} 行数据")
            return df
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return None

    def export_table_to_csv(self, table_key, date_filter=None, output_dir='./exports'):
        """
        导出指定表数据到CSV
        
        Args:
            table_key (str): 表配置键名
            date_filter (str): 日期过滤条件，格式如 '2024-01-01'
            output_dir (str): 输出目录
        """
        if table_key not in self.table_configs:
            logger.error(f"未找到表配置: {table_key}")
            return False
            
        config = self.table_configs[table_key]
        table_name = config['table_name']
        columns = config['columns']
        csv_headers = config['csv_headers']
        filename_prefix = config['filename_prefix']
        
        # 构建SQL查询
        columns_str = ', '.join(columns)
        query = f"SELECT {columns_str} FROM {table_name}"

        # 如果没有指定日期过滤，默认使用昨天的日期
        if date_filter is None:
            from datetime import datetime, timedelta
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
            date_filter = yesterday
            logger.info(f"未指定日期过滤，默认使用昨天日期: {date_filter}")

        # 确保日期格式为yyyymmdd
        if date_filter:
            # 如果日期包含连字符，转换为yyyymmdd格式
            if '-' in date_filter:
                try:
                    date_obj = datetime.strptime(date_filter, '%Y-%m-%d')
                    date_filter = date_obj.strftime('%Y%m%d')
                except ValueError:
                    logger.warning(f"日期格式不正确: {date_filter}，请使用 YYYY-MM-DD 或 YYYYMMDD 格式")

            query += f" WHERE ds = '{date_filter}'"

        # 添加限制条件
        query += " LIMIT 100"

        logger.info(f"执行查询: {query}")
        
        # 执行查询
        df = self.execute_query(query)
        if df is None:
            return False
            
        # 设置CSV列名
        df.columns = csv_headers
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        env_suffix = f"_{self.environment}" if self.environment == 'UAT' else ""
        filename = f"{filename_prefix}{env_suffix}_{self.timestamp}.csv"
        filepath = os.path.join(output_dir, filename)
        
        # 导出CSV
        try:
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"数据已导出到: {filepath}")
            logger.info(f"导出行数: {len(df)}")
            return True
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            return False

    def export_all_tables(self, date_filter=None, output_dir='./exports'):
        """导出所有表数据"""
        if not self.connect_database():
            return False
            
        try:
            success_count = 0
            for table_key in self.table_configs.keys():
                logger.info(f"开始导出表: {table_key}")
                if self.export_table_to_csv(table_key, date_filter, output_dir):
                    success_count += 1
                else:
                    logger.error(f"表 {table_key} 导出失败")
                    
            logger.info(f"导出完成，成功导出 {success_count}/{len(self.table_configs)} 个表")
            return success_count == len(self.table_configs)
            
        finally:
            self.disconnect_database()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数仓数据导出工具')
    parser.add_argument('--env', choices=['UAT', 'PROD'], default='UAT', help='环境标识')
    parser.add_argument('--date', help='日期过滤，格式: YYYY-MM-DD 或 YYYYMMDD，默认为昨天')
    parser.add_argument('--table', help='指定导出的表: store_sku_full, mt_sku_vender_agg, elem_store_sku_agg, elem_sku_region_agg')
    parser.add_argument('--output', default='./exports', help='输出目录')
    
    args = parser.parse_args()
    
    # 创建导出器
    exporter = DataWarehouseExporter(environment=args.env)
    
    if args.table:
        # 导出指定表
        if not exporter.connect_database():
            return
        try:
            exporter.export_table_to_csv(args.table, args.date, args.output)
        finally:
            exporter.disconnect_database()
    else:
        # 导出所有表
        exporter.export_all_tables(args.date, args.output)


if __name__ == "__main__":
    main()
