#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP文件上传工具
支持单文件和批量文件上传到指定的SFTP服务器
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Optional, Union
import paramiko
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sftp_upload.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class SFTPUploader:
    """SFTP文件上传器"""
    
    def __init__(self, host: str = None, port: int = None, username: str = None, 
                 password: str = None, remote_path: str = None):
        """
        初始化SFTP上传器
        
        Args:
            host: SFTP服务器地址
            port: SFTP服务器端口
            username: 用户名
            password: 密码
            remote_path: 远程目录路径
        """
        self.host = host or os.getenv('SFTP_HOST')
        self.port = int(port or os.getenv('SFTP_PORT', 22))
        self.username = username or os.getenv('SFTP_USERNAME')
        self.password = password or os.getenv('SFTP_PASSWORD')
        self.remote_path = remote_path or os.getenv('SFTP_REMOTE_PATH')
        
        self.client = None
        self.sftp = None
        
        # 验证必要参数
        if not all([self.host, self.username, self.password, self.remote_path]):
            raise ValueError("缺少必要的SFTP连接参数")
    
    def connect(self) -> bool:
        """
        连接到SFTP服务器
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            logger.info(f"正在连接到SFTP服务器: {self.host}:{self.port}")
            self.client.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                timeout=30
            )
            
            self.sftp = self.client.open_sftp()
            logger.info("SFTP连接成功")
            return True
            
        except Exception as e:
            logger.error(f"SFTP连接失败: {str(e)}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """断开SFTP连接"""
        try:
            if self.sftp:
                self.sftp.close()
                self.sftp = None
            if self.client:
                self.client.close()
                self.client = None
            logger.info("SFTP连接已断开")
        except Exception as e:
            logger.error(f"断开连接时出错: {str(e)}")
    def upload_file(self, local_file: Union[str, Path], remote_filename: str = None) -> bool:
        """
        上传单个文件

        Args:
            local_file: 本地文件路径
            remote_filename: 远程文件名（可选，默认使用本地文件名）

        Returns:
            bool: 上传是否成功
        """
        local_file = Path(local_file)

        if not local_file.exists():
            logger.error(f"本地文件不存在: {local_file}")
            return False

        if not local_file.is_file():
            logger.error(f"路径不是文件: {local_file}")
            return False

        try:
            # 确定远程文件名
            if remote_filename is None:
                remote_filename = local_file.name

            remote_file_path = f"{self.remote_path}/{remote_filename}"

            logger.info(f"开始上传文件: {local_file} -> {remote_file_path}")

            # 上传文件
            self.sftp.put(str(local_file), remote_file_path)

            # 验证上传
            remote_stat = self.sftp.stat(remote_file_path)
            local_size = local_file.stat().st_size

            if remote_stat.st_size == local_size:
                logger.info(f"文件上传成功: {remote_filename} ({local_size} bytes)")
                return True
            else:
                logger.error(f"文件上传验证失败: 本地大小={local_size}, 远程大小={remote_stat.st_size}")
                return False

        except Exception as e:
            logger.error(f"上传文件失败 {local_file}: {str(e)}")
            return False
    
    def upload_files(self, file_list: List[Union[str, Path]]) -> dict:
        """
        批量上传文件

        Args:
            file_list: 文件路径列表

        Returns:
            dict: 上传结果统计
        """
        results = {
            'success': [],
            'failed': [],
            'total': len(file_list)
        }

        for file_path in file_list:
            file_path = Path(file_path)

            if self.upload_file(file_path):
                results['success'].append(str(file_path))
            else:
                results['failed'].append(str(file_path))

        logger.info(f"批量上传完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
        return results


def main():
    """主函数 - 命令行使用示例"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python sftp_uploader.py <文件路径>")
        print("  python sftp_uploader.py <目录路径> --directory")
        print("  python sftp_uploader.py file1.txt file2.txt file3.txt")
        return
    
    uploader = SFTPUploader()
    
    try:
        if not uploader.connect():
            logger.error("无法连接到SFTP服务器")
            return
        
        if "--directory" in sys.argv:
            # 上传目录
            dir_path = sys.argv[1]
            results = uploader.upload_directory(dir_path)
        else:
            # 上传文件
            file_paths = [arg for arg in sys.argv[1:] if not arg.startswith('--')]
            if len(file_paths) == 1:
                success = uploader.upload_file(file_paths[0])
                results = {
                    'success': [file_paths[0]] if success else [],
                    'failed': [] if success else [file_paths[0]],
                    'total': 1
                }
            else:
                results = uploader.upload_files(file_paths)
        
        print(f"\n上传结果:")
        print(f"总计: {results['total']}")
        print(f"成功: {len(results['success'])}")
        print(f"失败: {len(results['failed'])}")
        
        if results['failed']:
            print(f"失败的文件: {results['failed']}")
            
    finally:
        uploader.disconnect()


if __name__ == "__main__":
    main()
